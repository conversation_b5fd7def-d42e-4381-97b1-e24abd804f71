#!/usr/bin/env python3
"""
Demo script to test live statistics functionality
"""
import sqlite3
import time
from datetime import datetime
import random

def create_demo_data():
    """Create some demo campaigns and sessions to show live stats"""
    
    # Connect to database
    conn = sqlite3.connect('youtube_campaigns.db', check_same_thread=False)
    cursor = conn.cursor()
    
    # Create demo campaigns
    demo_campaigns = [
        ("https://www.youtube.com/watch?v=dQw4w9WgXcQ", 1000, 100),
        ("https://youtu.be/9bZkp7q19f0", 500, 50),
        ("https://www.youtube.com/watch?v=kJQP7kiw5Fk", 2000, 200),
    ]
    
    print("🎬 Creating demo campaigns...")
    
    for url, target_views, target_watch_time in demo_campaigns:
        cursor.execute('''
            INSERT INTO campaigns (video_url, target_views, target_watch_time, created_date, status)
            VALUES (?, ?, ?, ?, ?)
        ''', (url, target_views, target_watch_time, datetime.now().isoformat(), 'pending'))
    
    conn.commit()
    
    # Get campaign IDs
    cursor.execute("SELECT id FROM campaigns ORDER BY id DESC LIMIT 3")
    campaign_ids = [row[0] for row in cursor.fetchall()]
    
    print("📊 Generating demo sessions...")
    
    # Create demo sessions
    device_types = ['Windows_Chrome', 'Mac_Safari', 'Android_Chrome', 'iPhone_Safari']
    engagement_types = ['view_only', 'like', 'subscribe', 'comment', 'like,view_only']
    
    for campaign_id in campaign_ids:
        # Generate random sessions
        num_sessions = random.randint(10, 50)
        total_views = 0
        total_watch_time = 0
        
        for _ in range(num_sessions):
            device = random.choice(device_types)
            watch_duration = random.randint(30, 600)
            engagement = random.choice(engagement_types)
            success = random.choice([True, True, True, False])  # 75% success rate
            
            cursor.execute('''
                INSERT INTO sessions (campaign_id, device_type, watch_duration, engagement_type, timestamp, success)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (campaign_id, device, watch_duration, engagement, datetime.now().isoformat(), success))
            
            if success:
                total_views += 1
                total_watch_time += watch_duration // 60  # Convert to minutes
        
        # Update campaign progress
        cursor.execute('''
            UPDATE campaigns 
            SET completed_views = ?, completed_watch_time = ?, status = ?
            WHERE id = ?
        ''', (total_views, total_watch_time, 'running' if random.random() > 0.5 else 'pending', campaign_id))
    
    conn.commit()
    conn.close()
    
    print("✅ Demo data created successfully!")
    print("🔄 Live statistics should now show real data in the app!")
    print("\n📈 Expected stats:")
    print("- Total Campaigns: 3+")
    print("- Active Campaigns: 1-2")
    print("- Total Views: 30-150")
    print("- Total Watch Time: Variable")
    print("- Success Rate: ~75%")

if __name__ == "__main__":
    create_demo_data()
