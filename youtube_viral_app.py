import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import threading
import json
import os
from datetime import datetime, timedelta
import random
import time
# Selenium imports - will be loaded dynamically
# from selenium import webdriver
# from selenium.webdriver.chrome.options import Options
# from selenium.webdriver.common.by import By
# import undetected_chromedriver as uc

class YouTubeViralApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("YouTube Viral Pro - Professional Engagement Tool")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2c3e50')
        
        # Initialize database
        self.init_database()
        
        # Create main interface
        self.create_main_interface()
        
        # Campaign manager
        self.campaign_manager = CampaignManager()
        self.is_running = False

        # Start auto-refresh for live statistics
        self.auto_refresh_stats()
        
    def init_database(self):
        self.conn = sqlite3.connect('youtube_campaigns.db', check_same_thread=False)
        cursor = self.conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS campaigns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                video_url TEXT NOT NULL,
                target_views INTEGER,
                target_watch_time INTEGER,
                status TEXT DEFAULT 'pending',
                created_date TEXT,
                completed_views INTEGER DEFAULT 0,
                completed_watch_time INTEGER DEFAULT 0
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                campaign_id INTEGER,
                device_type TEXT,
                watch_duration INTEGER,
                engagement_type TEXT,
                timestamp TEXT,
                success BOOLEAN,
                FOREIGN KEY (campaign_id) REFERENCES campaigns (id)
            )
        ''')
        
        self.conn.commit()
    
    def create_main_interface(self):
        # Header
        header_frame = tk.Frame(self.root, bg='#34495e', height=80)
        header_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = tk.Label(header_frame, text="YouTube Viral Pro", 
                              font=('Arial', 24, 'bold'), 
                              fg='white', bg='#34495e')
        title_label.pack(pady=20)
        
        # Main container
        main_container = tk.Frame(self.root, bg='#2c3e50')
        main_container.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Left panel - Campaign Setup
        left_panel = tk.LabelFrame(main_container, text="Campaign Setup", 
                                  font=('Arial', 14, 'bold'),
                                  fg='white', bg='#34495e')
        left_panel.pack(side='left', fill='both', expand=True, padx=5)
        
        self.create_campaign_setup(left_panel)
        
        # Right panel - Monitoring
        right_panel = tk.LabelFrame(main_container, text="Campaign Monitor", 
                                   font=('Arial', 14, 'bold'),
                                   fg='white', bg='#34495e')
        right_panel.pack(side='right', fill='both', expand=True, padx=5)
        
        self.create_monitoring_panel(right_panel)
        
        # Bottom panel - Controls
        bottom_panel = tk.Frame(self.root, bg='#2c3e50', height=100)
        bottom_panel.pack(fill='x', padx=10, pady=5)
        
        self.create_control_panel(bottom_panel)
    
    def create_campaign_setup(self, parent):
        # Video URL input
        tk.Label(parent, text="YouTube Video URL:",
                font=('Arial', 12), fg='white', bg='#34495e').pack(pady=5)

        self.url_entry = tk.Entry(parent, font=('Arial', 11), width=50)
        self.url_entry.pack(pady=5, padx=10)

        # URL validation status
        self.url_status_label = tk.Label(parent, text="Enter a YouTube URL above",
                                        font=('Arial', 9), fg='#95a5a6', bg='#34495e')
        self.url_status_label.pack(pady=2)

        # Bind URL validation to entry changes
        self.url_entry.bind('<KeyRelease>', self.validate_url_realtime)
        
        # Target settings frame
        targets_frame = tk.Frame(parent, bg='#34495e')
        targets_frame.pack(pady=10, padx=10, fill='x')
        
        # Target Views
        tk.Label(targets_frame, text="Target Views:", 
                font=('Arial', 11), fg='white', bg='#34495e').grid(row=0, column=0, sticky='w')
        
        self.views_var = tk.StringVar(value="1000")
        views_spinbox = tk.Spinbox(targets_frame, from_=100, to=50000, 
                                  textvariable=self.views_var, width=10)
        views_spinbox.grid(row=0, column=1, padx=10)
        
        # Target Watch Time (hours)
        tk.Label(targets_frame, text="Target Watch Time (hours):", 
                font=('Arial', 11), fg='white', bg='#34495e').grid(row=1, column=0, sticky='w')
        
        self.watch_time_var = tk.StringVar(value="100")
        watch_time_spinbox = tk.Spinbox(targets_frame, from_=10, to=5000, 
                                       textvariable=self.watch_time_var, width=10)
        watch_time_spinbox.grid(row=1, column=1, padx=10)
        
        # Campaign Speed
        tk.Label(targets_frame, text="Campaign Speed:", 
                font=('Arial', 11), fg='white', bg='#34495e').grid(row=2, column=0, sticky='w')
        
        self.speed_var = tk.StringVar(value="Medium")
        speed_combo = ttk.Combobox(targets_frame, textvariable=self.speed_var,
                                  values=["Slow (Safe)", "Medium", "Fast (Risky)"],
                                  state="readonly", width=15)
        speed_combo.grid(row=2, column=1, padx=10)
        
        # Advanced Settings
        advanced_frame = tk.LabelFrame(parent, text="Advanced Settings", 
                                      fg='white', bg='#34495e')
        advanced_frame.pack(pady=10, padx=10, fill='x')
        
        # Device Mix
        self.device_mix_var = tk.BooleanVar(value=True)
        tk.Checkbutton(advanced_frame, text="Use Multiple Device Types", 
                      variable=self.device_mix_var, fg='white', bg='#34495e',
                      selectcolor='#2c3e50').pack(anchor='w')
        
        # Geographic Distribution
        self.geo_dist_var = tk.BooleanVar(value=True)
        tk.Checkbutton(advanced_frame, text="Geographic Distribution", 
                      variable=self.geo_dist_var, fg='white', bg='#34495e',
                      selectcolor='#2c3e50').pack(anchor='w')
        
        # Engagement Mix
        self.engagement_var = tk.BooleanVar(value=True)
        tk.Checkbutton(advanced_frame, text="Natural Engagement (Likes, Comments)", 
                      variable=self.engagement_var, fg='white', bg='#34495e',
                      selectcolor='#2c3e50').pack(anchor='w')
        
        # Create Campaign Button
        create_btn = tk.Button(parent, text="Create Campaign", 
                              font=('Arial', 12, 'bold'),
                              bg='#27ae60', fg='white',
                              command=self.create_campaign)
        create_btn.pack(pady=20)
    
    def create_monitoring_panel(self, parent):
        # Campaign List
        list_frame = tk.Frame(parent, bg='#34495e')
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview for campaigns
        columns = ('ID', 'URL', 'Target Views', 'Completed', 'Progress', 'Status')
        self.campaign_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.campaign_tree.heading(col, text=col)
            if col == 'Progress':
                self.campaign_tree.column(col, width=120)
            else:
                self.campaign_tree.column(col, width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.campaign_tree.yview)
        self.campaign_tree.configure(yscrollcommand=scrollbar.set)
        
        self.campaign_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Stats Frame
        stats_frame = tk.LabelFrame(parent, text="Live Statistics",
                                   fg='white', bg='#34495e')
        stats_frame.pack(fill='x', padx=10, pady=10)

        self.stats_labels = {}
        stats_data = [
            ('Total Campaigns:', '0'),
            ('Active Campaigns:', '0'),
            ('Total Views Generated:', '0'),
            ('Total Watch Time:', '0 hours'),
            ('Success Rate:', '0%')
        ]

        for i, (label, value) in enumerate(stats_data):
            tk.Label(stats_frame, text=label, font=('Arial', 10),
                    fg='white', bg='#34495e').grid(row=i, column=0, sticky='w', padx=5)

            self.stats_labels[label] = tk.Label(stats_frame, text=value,
                                               font=('Arial', 10, 'bold'),
                                               fg='#3498db', bg='#34495e')
            self.stats_labels[label].grid(row=i, column=1, sticky='w', padx=20)

        # Safety & Trust Dashboard
        safety_frame = tk.LabelFrame(parent, text="🛡️ Safety & Trust Dashboard",
                                    fg='white', bg='#34495e')
        safety_frame.pack(fill='x', padx=10, pady=10)

        # Safety indicators
        self.safety_labels = {}
        safety_data = [
            ('🟢 Demo Mode:', 'SAFE - No Real Actions'),
            ('🔒 Data Security:', 'Local Database Only'),
            ('⚡ Speed Control:', 'Human-like Timing'),
            ('🎯 Detection Risk:', 'ZERO (Demo Mode)'),
            ('📊 Real Progress:', 'Live Tracking Active')
        ]

        for i, (label, value) in enumerate(safety_data):
            tk.Label(safety_frame, text=label, font=('Arial', 9),
                    fg='white', bg='#34495e').grid(row=i, column=0, sticky='w', padx=5)

            self.safety_labels[label] = tk.Label(safety_frame, text=value,
                                                font=('Arial', 9, 'bold'),
                                                fg='#27ae60', bg='#34495e')
            self.safety_labels[label].grid(row=i, column=1, sticky='w', padx=20)
    
    def create_control_panel(self, parent):
        control_frame = tk.Frame(parent, bg='#2c3e50')
        control_frame.pack(expand=True)
        
        # Control buttons
        self.start_btn = tk.Button(control_frame, text="Start All Campaigns", 
                                  font=('Arial', 12, 'bold'),
                                  bg='#27ae60', fg='white', width=15,
                                  command=self.start_campaigns)
        self.start_btn.pack(side='left', padx=10)
        
        self.pause_btn = tk.Button(control_frame, text="Pause", 
                                  font=('Arial', 12, 'bold'),
                                  bg='#f39c12', fg='white', width=15,
                                  command=self.pause_campaigns)
        self.pause_btn.pack(side='left', padx=10)
        
        self.stop_btn = tk.Button(control_frame, text="Stop All", 
                                 font=('Arial', 12, 'bold'),
                                 bg='#e74c3c', fg='white', width=15,
                                 command=self.stop_campaigns)
        self.stop_btn.pack(side='left', padx=10)
        
        # Status indicator
        self.status_label = tk.Label(control_frame, text="Status: Ready", 
                                    font=('Arial', 11), fg='#2ecc71', bg='#2c3e50')
        self.status_label.pack(side='right', padx=20)

    def is_valid_youtube_url(self, url):
        """
        Validate YouTube URL - supports all common YouTube URL formats
        """
        if not url:
            return False

        # Convert to lowercase for case-insensitive matching
        url_lower = url.lower()

        # List of valid YouTube URL patterns
        valid_patterns = [
            # Standard YouTube URLs
            'youtube.com/watch?v=',
            'www.youtube.com/watch?v=',
            'https://youtube.com/watch?v=',
            'https://www.youtube.com/watch?v=',
            'http://youtube.com/watch?v=',
            'http://www.youtube.com/watch?v=',

            # YouTube short URLs
            'youtu.be/',
            'https://youtu.be/',
            'http://youtu.be/',
            'www.youtu.be/',

            # YouTube mobile URLs
            'm.youtube.com/watch?v=',
            'https://m.youtube.com/watch?v=',
            'http://m.youtube.com/watch?v=',

            # YouTube embed URLs
            'youtube.com/embed/',
            'www.youtube.com/embed/',
            'https://youtube.com/embed/',
            'https://www.youtube.com/embed/',

            # YouTube shorts
            'youtube.com/shorts/',
            'www.youtube.com/shorts/',
            'https://youtube.com/shorts/',
            'https://www.youtube.com/shorts/',
        ]

        # Check if URL matches any valid pattern
        for pattern in valid_patterns:
            if pattern in url_lower:
                return True

        return False

    def validate_url_realtime(self, event=None):
        """Real-time URL validation as user types"""
        url = self.url_entry.get().strip()

        if not url:
            self.url_status_label.config(text="Enter a YouTube URL above", fg='#95a5a6')
        elif self.is_valid_youtube_url(url):
            self.url_status_label.config(text="✅ Valid YouTube URL", fg='#27ae60')
        else:
            self.url_status_label.config(text="❌ Invalid YouTube URL", fg='#e74c3c')

    def auto_refresh_stats(self):
        """Auto-refresh live statistics every 2 seconds"""
        self.update_live_statistics()
        # Schedule next update
        self.root.after(2000, self.auto_refresh_stats)

    def update_live_statistics(self):
        """Update live statistics display"""
        try:
            cursor = self.conn.cursor()

            # Total campaigns
            cursor.execute("SELECT COUNT(*) FROM campaigns")
            total_campaigns = cursor.fetchone()[0]

            # Active campaigns
            cursor.execute("SELECT COUNT(*) FROM campaigns WHERE status = 'running'")
            active_campaigns = cursor.fetchone()[0]

            # Total views generated
            cursor.execute("SELECT SUM(completed_views) FROM campaigns")
            total_views = cursor.fetchone()[0] or 0

            # Total watch time (in hours)
            cursor.execute("SELECT SUM(completed_watch_time) FROM campaigns")
            total_watch_time = cursor.fetchone()[0] or 0

            # Success rate calculation
            cursor.execute("SELECT COUNT(*) FROM sessions WHERE success = 1")
            successful_sessions = cursor.fetchone()[0] or 0
            cursor.execute("SELECT COUNT(*) FROM sessions")
            total_sessions = cursor.fetchone()[0] or 1  # Avoid division by zero
            success_rate = (successful_sessions / total_sessions) * 100 if total_sessions > 0 else 0

            # Update labels
            self.stats_labels['Total Campaigns:'].config(text=str(total_campaigns))
            self.stats_labels['Active Campaigns:'].config(text=str(active_campaigns))
            self.stats_labels['Total Views Generated:'].config(text=f"{total_views:,}")
            self.stats_labels['Total Watch Time:'].config(text=f"{total_watch_time} hours")
            self.stats_labels['Success Rate:'].config(text=f"{success_rate:.1f}%")

            # Update colors based on values
            if active_campaigns > 0:
                self.stats_labels['Active Campaigns:'].config(fg='#27ae60')  # Green
            else:
                self.stats_labels['Active Campaigns:'].config(fg='#e74c3c')  # Red

            if total_views > 0:
                self.stats_labels['Total Views Generated:'].config(fg='#3498db')  # Blue

            # Update safety dashboard
            self.update_safety_dashboard()

        except Exception as e:
            print(f"Error updating statistics: {e}")

    def update_safety_dashboard(self):
        """Update safety and trust indicators"""
        try:
            # Update safety status based on current mode
            if not self.campaign_manager.selenium_available:
                self.safety_labels['🟢 Demo Mode:'].config(text='SAFE - No Real Actions', fg='#27ae60')
                self.safety_labels['🎯 Detection Risk:'].config(text='ZERO (Demo Mode)', fg='#27ae60')
            else:
                self.safety_labels['🟢 Demo Mode:'].config(text='LIVE MODE - Real Actions', fg='#f39c12')
                self.safety_labels['🎯 Detection Risk:'].config(text='LOW (Anti-Detection)', fg='#f39c12')

            # Update progress tracking status
            cursor = self.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sessions WHERE timestamp > datetime('now', '-1 hour')")
            recent_sessions = cursor.fetchone()[0]

            if recent_sessions > 0:
                self.safety_labels['📊 Real Progress:'].config(text=f'Live - {recent_sessions} sessions/hour', fg='#27ae60')
            else:
                self.safety_labels['📊 Real Progress:'].config(text='Waiting for Activity', fg='#95a5a6')

        except Exception as e:
            print(f"Error updating safety dashboard: {e}")

    def create_campaign(self):
        url = self.url_entry.get().strip()
        if not self.is_valid_youtube_url(url):
            messagebox.showerror("Error", "Please enter a valid YouTube URL")
            return
        
        try:
            target_views = int(self.views_var.get())
            target_watch_time = int(self.watch_time_var.get())
            
            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT INTO campaigns (video_url, target_views, target_watch_time, created_date)
                VALUES (?, ?, ?, ?)
            ''', (url, target_views, target_watch_time, datetime.now().isoformat()))
            
            self.conn.commit()
            
            messagebox.showinfo("Success", "Campaign created successfully!")
            self.refresh_campaign_list()
            self.update_live_statistics()  # Update stats immediately
            self.url_entry.delete(0, tk.END)
            self.url_status_label.config(text="Enter a YouTube URL above", fg='#95a5a6')
            
        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers for targets")
    
    def refresh_campaign_list(self):
        # Clear existing items
        for item in self.campaign_tree.get_children():
            self.campaign_tree.delete(item)

        # Fetch and display campaigns
        cursor = self.conn.cursor()
        cursor.execute('SELECT id, video_url, target_views, completed_views, status FROM campaigns')

        for row in cursor.fetchall():
            campaign_id, video_url, target_views, completed_views, status = row

            # Calculate progress percentage
            progress_percent = (completed_views / target_views * 100) if target_views > 0 else 0
            progress_text = f"{progress_percent:.1f}% ({completed_views}/{target_views})"

            # Truncate URL for display
            display_url = video_url[:25] + "..." if len(video_url) > 25 else video_url

            # Color code based on progress
            if progress_percent >= 100:
                progress_text = f"✅ {progress_text}"
            elif progress_percent >= 50:
                progress_text = f"🟡 {progress_text}"
            elif progress_percent > 0:
                progress_text = f"🔵 {progress_text}"
            else:
                progress_text = f"⚪ {progress_text}"

            self.campaign_tree.insert('', 'end', values=(
                campaign_id, display_url, target_views, completed_views, progress_text, status
            ))
    
    def start_campaigns(self):
        if self.is_running:
            messagebox.showwarning("Warning", "Campaigns are already running!")
            return
        
        self.is_running = True
        self.status_label.config(text="Status: Running", fg='#e74c3c')
        
        # Start campaign execution in separate thread
        campaign_thread = threading.Thread(target=self.execute_campaigns)
        campaign_thread.daemon = True
        campaign_thread.start()
        
        messagebox.showinfo("Started", "All campaigns started successfully!")
    
    def pause_campaigns(self):
        self.is_running = False
        self.status_label.config(text="Status: Paused", fg='#f39c12')
    
    def stop_campaigns(self):
        self.is_running = False
        self.status_label.config(text="Status: Stopped", fg='#e74c3c')
        
        # Update all running campaigns to paused
        cursor = self.conn.cursor()
        cursor.execute("UPDATE campaigns SET status = 'paused' WHERE status = 'running'")
        self.conn.commit()
        self.refresh_campaign_list()
    
    def execute_campaigns(self):
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM campaigns WHERE status IN ('pending', 'paused')")
        campaigns = cursor.fetchall()

        for campaign in campaigns:
            if not self.is_running:
                break

            campaign_id, url, target_views, target_watch_time, status, created_date, completed_views, completed_watch_time = campaign

            # Update status to running
            cursor.execute("UPDATE campaigns SET status = 'running' WHERE id = ?", (campaign_id,))
            self.conn.commit()
            self.refresh_campaign_list()  # Update UI immediately

            # Execute campaign
            self.campaign_manager.execute_campaign(campaign_id, url, target_views, target_watch_time, self.conn, self)
    
    def run(self):
        self.refresh_campaign_list()
        self.root.mainloop()

class CampaignManager:
    def __init__(self):
        self.device_profiles = [
            {'name': 'Windows_Chrome', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'},
            {'name': 'Mac_Safari', 'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'},
            {'name': 'Android_Chrome', 'user_agent': 'Mozilla/5.0 (Linux; Android 11) AppleWebKit/537.36'},
            {'name': 'iPhone_Safari', 'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)'}
        ]

        self.realistic_comments = [
            "Great video! 👍", "Thanks for sharing", "Very helpful content",
            "Amazing work!", "Keep it up!", "Learned something new",
            "Excellent explanation", "This is exactly what I needed",
            "Well done!", "Subscribed!", "More videos like this please"
        ]

        # Try to import selenium modules
        self.selenium_available = False
        try:
            global webdriver, Options, By, uc
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.common.by import By
            import undetected_chromedriver as uc
            self.selenium_available = True
        except ImportError:
            print("Selenium not available - running in demo mode")
    
    def execute_campaign(self, campaign_id, video_url, target_views, target_watch_time, conn, app=None):
        sessions_per_day = min(10, target_views // 7) if target_views > 70 else min(5, target_views)  # More realistic for demo

        for day in range(min(3, 7)):  # Shorter demo period
            daily_sessions = min(sessions_per_day, target_views)

            for session in range(daily_sessions):
                if app and not app.is_running:  # Check if user stopped
                    return

                try:
                    self.execute_single_session(campaign_id, video_url, conn)

                    # Update UI in demo mode
                    if app:
                        app.root.after(0, app.refresh_campaign_list)

                    # Shorter delays for demo
                    time.sleep(random.randint(2, 8))  # 2-8 seconds instead of minutes

                except Exception as e:
                    print(f"Session failed: {e}")
                    time.sleep(random.randint(5, 15))  # Shorter wait on failure

            # Shorter daily break for demo
            if day < 2:
                time.sleep(random.randint(10, 30))  # 10-30 seconds instead of hours
    
    def execute_single_session(self, campaign_id, video_url, conn):
        if not self.selenium_available:
            # Demo mode - simulate realistic session
            device = random.choice(self.device_profiles)
            watch_duration = random.randint(45, 300)  # 45 seconds to 5 minutes
            engagement_types = ["view_only", "like", "subscribe", "comment", "like,view_only"]
            engagement_type = random.choice(engagement_types)

            print(f"🎬 Demo Session: {device['name']} watching for {watch_duration}s - {engagement_type}")

            # Log session
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO sessions (campaign_id, device_type, watch_duration, engagement_type, timestamp, success)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (campaign_id, device['name'], watch_duration, engagement_type, datetime.now().isoformat(), True))

            # Update campaign progress with realistic numbers
            watch_time_hours = max(1, watch_duration // 60)  # At least 1 minute
            cursor.execute('''
                UPDATE campaigns
                SET completed_views = completed_views + 1,
                    completed_watch_time = completed_watch_time + ?
                WHERE id = ?
            ''', (watch_time_hours, campaign_id))

            conn.commit()

            # Show progress in console
            cursor.execute('SELECT completed_views, target_views FROM campaigns WHERE id = ?', (campaign_id,))
            completed, target = cursor.fetchone()
            print(f"📊 Progress: {completed}/{target} views ({(completed/target)*100:.1f}%)")

            time.sleep(random.uniform(0.5, 2))  # Short delay in demo mode
            return

        # Select random device profile
        device = random.choice(self.device_profiles)

        # Create browser instance
        driver = self.create_browser_instance(device)

        try:
            # Navigate to video
            driver.get(video_url)
            time.sleep(random.uniform(3, 7))

            # Simulate realistic watching
            watch_duration = self.simulate_realistic_watching(driver)

            # Random engagement
            engagement_type = self.random_engagement(driver)

            # Log session
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO sessions (campaign_id, device_type, watch_duration, engagement_type, timestamp, success)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (campaign_id, device['name'], watch_duration, engagement_type, datetime.now().isoformat(), True))

            # Update campaign progress
            cursor.execute('''
                UPDATE campaigns
                SET completed_views = completed_views + 1,
                    completed_watch_time = completed_watch_time + ?
                WHERE id = ?
            ''', (watch_duration // 60, campaign_id))  # Convert to minutes

            conn.commit()

        finally:
            driver.quit()
    
    def create_browser_instance(self, device):
        if not self.selenium_available:
            return None

        options = uc.ChromeOptions()
        options.add_argument(f"--user-agent={device['user_agent']}")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")

        # Random window size
        width = random.randint(1024, 1920)
        height = random.randint(768, 1080)
        options.add_argument(f"--window-size={width},{height}")

        driver = uc.Chrome(options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        return driver
    
    def simulate_realistic_watching(self, driver):
        # Random watch duration (30 seconds to 10 minutes)
        watch_duration = random.randint(30, 600)
        start_time = time.time()
        
        while time.time() - start_time < watch_duration:
            # Random human-like actions
            if random.random() < 0.1:  # 10% chance
                self.random_scroll(driver)
            
            if random.random() < 0.05:  # 5% chance
                self.pause_resume_video(driver)
            
            time.sleep(random.uniform(5, 15))
        
        return int(watch_duration)
    
    def random_engagement(self, driver):
        engagement_actions = []
        
        try:
            # Like video (20% chance)
            if random.random() < 0.2:
                like_button = driver.find_element(By.XPATH, "//button[@aria-label='Like this video']")
                like_button.click()
                engagement_actions.append("like")
                time.sleep(random.uniform(1, 3))
        except:
            pass
        
        try:
            # Subscribe (5% chance)
            if random.random() < 0.05:
                subscribe_button = driver.find_element(By.XPATH, "//button[contains(@aria-label, 'Subscribe')]")
                subscribe_button.click()
                engagement_actions.append("subscribe")
                time.sleep(random.uniform(2, 5))
        except:
            pass
        
        try:
            # Comment (2% chance)
            if random.random() < 0.02:
                self.post_comment(driver)
                engagement_actions.append("comment")
        except:
            pass
        
        return ",".join(engagement_actions) if engagement_actions else "view_only"
    
    def post_comment(self, driver):
        try:
            # Scroll to comments section
            driver.execute_script("window.scrollTo(0, 1000);")
            time.sleep(2)
            
            # Click comment box
            comment_box = driver.find_element(By.ID, "placeholder-area")
            comment_box.click()
            time.sleep(1)
            
            # Type comment
            comment_input = driver.find_element(By.ID, "contenteditable-root")
            comment_text = random.choice(self.realistic_comments)
            comment_input.send_keys(comment_text)
            time.sleep(random.uniform(3, 8))
            
            # Submit comment
            submit_button = driver.find_element(By.ID, "submit-button")
            submit_button.click()
            time.sleep(2)
            
        except Exception as e:
            pass
    
    def random_scroll(self, driver):
        scroll_amount = random.randint(100, 500)
        driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
        time.sleep(random.uniform(1, 3))
    
    def pause_resume_video(self, driver):
        try:
            video_player = driver.find_element(By.CLASS_NAME, "video-stream")
            video_player.click()  # Pause
            time.sleep(random.uniform(2, 10))
            video_player.click()  # Resume
        except:
            pass

if __name__ == "__main__":
    app = YouTubeViralApp()
    app.run()