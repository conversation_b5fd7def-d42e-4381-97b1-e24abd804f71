import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import threading
import json
import os
from datetime import datetime, timedelta
import random
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import undetected_chromedriver as uc

class YouTubeViralApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("YouTube Viral Pro - Professional Engagement Tool")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2c3e50')
        
        # Initialize database
        self.init_database()
        
        # Create main interface
        self.create_main_interface()
        
        # Campaign manager
        self.campaign_manager = CampaignManager()
        self.is_running = False
        
    def init_database(self):
        self.conn = sqlite3.connect('youtube_campaigns.db')
        cursor = self.conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS campaigns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                video_url TEXT NOT NULL,
                target_views INTEGER,
                target_watch_time INTEGER,
                status TEXT DEFAULT 'pending',
                created_date TEXT,
                completed_views INTEGER DEFAULT 0,
                completed_watch_time INTEGER DEFAULT 0
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                campaign_id INTEGER,
                device_type TEXT,
                watch_duration INTEGER,
                engagement_type TEXT,
                timestamp TEXT,
                success BOOLEAN,
                FOREIGN KEY (campaign_id) REFERENCES campaigns (id)
            )
        ''')
        
        self.conn.commit()
    
    def create_main_interface(self):
        # Header
        header_frame = tk.Frame(self.root, bg='#34495e', height=80)
        header_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = tk.Label(header_frame, text="YouTube Viral Pro", 
                              font=('Arial', 24, 'bold'), 
                              fg='white', bg='#34495e')
        title_label.pack(pady=20)
        
        # Main container
        main_container = tk.Frame(self.root, bg='#2c3e50')
        main_container.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Left panel - Campaign Setup
        left_panel = tk.LabelFrame(main_container, text="Campaign Setup", 
                                  font=('Arial', 14, 'bold'),
                                  fg='white', bg='#34495e')
        left_panel.pack(side='left', fill='both', expand=True, padx=5)
        
        self.create_campaign_setup(left_panel)
        
        # Right panel - Monitoring
        right_panel = tk.LabelFrame(main_container, text="Campaign Monitor", 
                                   font=('Arial', 14, 'bold'),
                                   fg='white', bg='#34495e')
        right_panel.pack(side='right', fill='both', expand=True, padx=5)
        
        self.create_monitoring_panel(right_panel)
        
        # Bottom panel - Controls
        bottom_panel = tk.Frame(self.root, bg='#2c3e50', height=100)
        bottom_panel.pack(fill='x', padx=10, pady=5)
        
        self.create_control_panel(bottom_panel)
    
    def create_campaign_setup(self, parent):
        # Video URL input
        tk.Label(parent, text="YouTube Video URL:", 
                font=('Arial', 12), fg='white', bg='#34495e').pack(pady=5)
        
        self.url_entry = tk.Entry(parent, font=('Arial', 11), width=50)
        self.url_entry.pack(pady=5, padx=10)
        
        # Target settings frame
        targets_frame = tk.Frame(parent, bg='#34495e')
        targets_frame.pack(pady=10, padx=10, fill='x')
        
        # Target Views
        tk.Label(targets_frame, text="Target Views:", 
                font=('Arial', 11), fg='white', bg='#34495e').grid(row=0, column=0, sticky='w')
        
        self.views_var = tk.StringVar(value="1000")
        views_spinbox = tk.Spinbox(targets_frame, from_=100, to=50000, 
                                  textvariable=self.views_var, width=10)
        views_spinbox.grid(row=0, column=1, padx=10)
        
        # Target Watch Time (hours)
        tk.Label(targets_frame, text="Target Watch Time (hours):", 
                font=('Arial', 11), fg='white', bg='#34495e').grid(row=1, column=0, sticky='w')
        
        self.watch_time_var = tk.StringVar(value="100")
        watch_time_spinbox = tk.Spinbox(targets_frame, from_=10, to=5000, 
                                       textvariable=self.watch_time_var, width=10)
        watch_time_spinbox.grid(row=1, column=1, padx=10)
        
        # Campaign Speed
        tk.Label(targets_frame, text="Campaign Speed:", 
                font=('Arial', 11), fg='white', bg='#34495e').grid(row=2, column=0, sticky='w')
        
        self.speed_var = tk.StringVar(value="Medium")
        speed_combo = ttk.Combobox(targets_frame, textvariable=self.speed_var,
                                  values=["Slow (Safe)", "Medium", "Fast (Risky)"],
                                  state="readonly", width=15)
        speed_combo.grid(row=2, column=1, padx=10)
        
        # Advanced Settings
        advanced_frame = tk.LabelFrame(parent, text="Advanced Settings", 
                                      fg='white', bg='#34495e')
        advanced_frame.pack(pady=10, padx=10, fill='x')
        
        # Device Mix
        self.device_mix_var = tk.BooleanVar(value=True)
        tk.Checkbutton(advanced_frame, text="Use Multiple Device Types", 
                      variable=self.device_mix_var, fg='white', bg='#34495e',
                      selectcolor='#2c3e50').pack(anchor='w')
        
        # Geographic Distribution
        self.geo_dist_var = tk.BooleanVar(value=True)
        tk.Checkbutton(advanced_frame, text="Geographic Distribution", 
                      variable=self.geo_dist_var, fg='white', bg='#34495e',
                      selectcolor='#2c3e50').pack(anchor='w')
        
        # Engagement Mix
        self.engagement_var = tk.BooleanVar(value=True)
        tk.Checkbutton(advanced_frame, text="Natural Engagement (Likes, Comments)", 
                      variable=self.engagement_var, fg='white', bg='#34495e',
                      selectcolor='#2c3e50').pack(anchor='w')
        
        # Create Campaign Button
        create_btn = tk.Button(parent, text="Create Campaign", 
                              font=('Arial', 12, 'bold'),
                              bg='#27ae60', fg='white',
                              command=self.create_campaign)
        create_btn.pack(pady=20)
    
    def create_monitoring_panel(self, parent):
        # Campaign List
        list_frame = tk.Frame(parent, bg='#34495e')
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview for campaigns
        columns = ('ID', 'URL', 'Target Views', 'Completed', 'Status')
        self.campaign_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.campaign_tree.heading(col, text=col)
            self.campaign_tree.column(col, width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.campaign_tree.yview)
        self.campaign_tree.configure(yscrollcommand=scrollbar.set)
        
        self.campaign_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Stats Frame
        stats_frame = tk.LabelFrame(parent, text="Live Statistics", 
                                   fg='white', bg='#34495e')
        stats_frame.pack(fill='x', padx=10, pady=10)
        
        self.stats_labels = {}
        stats_data = [
            ('Total Campaigns:', '0'),
            ('Active Campaigns:', '0'),
            ('Total Views Generated:', '0'),
            ('Total Watch Time:', '0 hours'),
            ('Success Rate:', '0%')
        ]
        
        for i, (label, value) in enumerate(stats_data):
            tk.Label(stats_frame, text=label, font=('Arial', 10), 
                    fg='white', bg='#34495e').grid(row=i, column=0, sticky='w', padx=5)
            
            self.stats_labels[label] = tk.Label(stats_frame, text=value, 
                                               font=('Arial', 10, 'bold'), 
                                               fg='#3498db', bg='#34495e')
            self.stats_labels[label].grid(row=i, column=1, sticky='w', padx=20)
    
    def create_control_panel(self, parent):
        control_frame = tk.Frame(parent, bg='#2c3e50')
        control_frame.pack(expand=True)
        
        # Control buttons
        self.start_btn = tk.Button(control_frame, text="Start All Campaigns", 
                                  font=('Arial', 12, 'bold'),
                                  bg='#27ae60', fg='white', width=15,
                                  command=self.start_campaigns)
        self.start_btn.pack(side='left', padx=10)
        
        self.pause_btn = tk.Button(control_frame, text="Pause", 
                                  font=('Arial', 12, 'bold'),
                                  bg='#f39c12', fg='white', width=15,
                                  command=self.pause_campaigns)
        self.pause_btn.pack(side='left', padx=10)
        
        self.stop_btn = tk.Button(control_frame, text="Stop All", 
                                 font=('Arial', 12, 'bold'),
                                 bg='#e74c3c', fg='white', width=15,
                                 command=self.stop_campaigns)
        self.stop_btn.pack(side='left', padx=10)
        
        # Status indicator
        self.status_label = tk.Label(control_frame, text="Status: Ready", 
                                    font=('Arial', 11), fg='#2ecc71', bg='#2c3e50')
        self.status_label.pack(side='right', padx=20)
    
    def create_campaign(self):
        url = self.url_entry.get().strip()
        if not url or 'youtube.com/watch' not in url:
            messagebox.showerror("Error", "Please enter a valid YouTube URL")
            return
        
        try:
            target_views = int(self.views_var.get())
            target_watch_time = int(self.watch_time_var.get())
            
            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT INTO campaigns (video_url, target_views, target_watch_time, created_date)
                VALUES (?, ?, ?, ?)
            ''', (url, target_views, target_watch_time, datetime.now().isoformat()))
            
            self.conn.commit()
            
            messagebox.showinfo("Success", "Campaign created successfully!")
            self.refresh_campaign_list()
            self.url_entry.delete(0, tk.END)
            
        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers for targets")
    
    def refresh_campaign_list(self):
        # Clear existing items
        for item in self.campaign_tree.get_children():
            self.campaign_tree.delete(item)
        
        # Fetch and display campaigns
        cursor = self.conn.cursor()
        cursor.execute('SELECT id, video_url, target_views, completed_views, status FROM campaigns')
        
        for row in cursor.fetchall():
            # Truncate URL for display
            display_url = row[1][:30] + "..." if len(row[1]) > 30 else row[1]
            self.campaign_tree.insert('', 'end', values=(
                row[0], display_url, row[2], row[3], row[4]
            ))
    
    def start_campaigns(self):
        if self.is_running:
            messagebox.showwarning("Warning", "Campaigns are already running!")
            return
        
        self.is_running = True
        self.status_label.config(text="Status: Running", fg='#e74c3c')
        
        # Start campaign execution in separate thread
        campaign_thread = threading.Thread(target=self.execute_campaigns)
        campaign_thread.daemon = True
        campaign_thread.start()
        
        messagebox.showinfo("Started", "All campaigns started successfully!")
    
    def pause_campaigns(self):
        self.is_running = False
        self.status_label.config(text="Status: Paused", fg='#f39c12')
    
    def stop_campaigns(self):
        self.is_running = False
        self.status_label.config(text="Status: Stopped", fg='#e74c3c')
        
        # Update all running campaigns to paused
        cursor = self.conn.cursor()
        cursor.execute("UPDATE campaigns SET status = 'paused' WHERE status = 'running'")
        self.conn.commit()
        self.refresh_campaign_list()
    
    def execute_campaigns(self):
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM campaigns WHERE status IN ('pending', 'paused')")
        campaigns = cursor.fetchall()
        
        for campaign in campaigns:
            if not self.is_running:
                break
                
            campaign_id, url, target_views, target_watch_time, status, created_date, completed_views, completed_watch_time = campaign
            
            # Update status to running
            cursor.execute("UPDATE campaigns SET status = 'running' WHERE id = ?", (campaign_id,))
            self.conn.commit()
            
            # Execute campaign
            self.campaign_manager.execute_campaign(campaign_id, url, target_views, target_watch_time, self.conn)
    
    def run(self):
        self.refresh_campaign_list()
        self.root.mainloop()

class CampaignManager:
    def __init__(self):
        self.device_profiles = [
            {'name': 'Windows_Chrome', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'},
            {'name': 'Mac_Safari', 'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'},
            {'name': 'Android_Chrome', 'user_agent': 'Mozilla/5.0 (Linux; Android 11) AppleWebKit/537.36'},
            {'name': 'iPhone_Safari', 'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)'}
        ]
        
        self.realistic_comments = [
            "Great video! 👍", "Thanks for sharing", "Very helpful content",
            "Amazing work!", "Keep it up!", "Learned something new",
            "Excellent explanation", "This is exactly what I needed",
            "Well done!", "Subscribed!", "More videos like this please"
        ]
    
    def execute_campaign(self, campaign_id, video_url, target_views, target_watch_time, conn):
        sessions_per_day = min(50, target_views // 7)  # Spread over week minimum
        
        for day in range(7):  # Run for a week
            daily_sessions = min(sessions_per_day, target_views)
            
            for session in range(daily_sessions):
                try:
                    self.execute_single_session(campaign_id, video_url, conn)
                    time.sleep(random.randint(300, 1800))  # 5-30 min gap
                    
                except Exception as e:
                    print(f"Session failed: {e}")
                    time.sleep(random.randint(600, 3600))  # Longer wait on failure
            
            # Daily break
            if day < 6:
                time.sleep(random.randint(14400, 28800))  # 4-8 hour break
    
    def execute_single_session(self, campaign_id, video_url, conn):
        # Select random device profile
        device = random.choice(self.device_profiles)
        
        # Create browser instance
        driver = self.create_browser_instance(device)
        
        try:
            # Navigate to video
            driver.get(video_url)
            time.sleep(random.uniform(3, 7))
            
            # Simulate realistic watching
            watch_duration = self.simulate_realistic_watching(driver)
            
            # Random engagement
            engagement_type = self.random_engagement(driver)
            
            # Log session
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO sessions (campaign_id, device_type, watch_duration, engagement_type, timestamp, success)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (campaign_id, device['name'], watch_duration, engagement_type, datetime.now().isoformat(), True))
            
            # Update campaign progress
            cursor.execute('''
                UPDATE campaigns 
                SET completed_views = completed_views + 1,
                    completed_watch_time = completed_watch_time + ?
                WHERE id = ?
            ''', (watch_duration // 60, campaign_id))  # Convert to minutes
            
            conn.commit()
            
        finally:
            driver.quit()
    
    def create_browser_instance(self, device):
        options = uc.ChromeOptions()
        options.add_argument(f"--user-agent={device['user_agent']}")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        
        # Random window size
        width = random.randint(1024, 1920)
        height = random.randint(768, 1080)
        options.add_argument(f"--window-size={width},{height}")
        
        driver = uc.Chrome(options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver
    
    def simulate_realistic_watching(self, driver):
        # Random watch duration (30 seconds to 10 minutes)
        watch_duration = random.randint(30, 600)
        start_time = time.time()
        
        while time.time() - start_time < watch_duration:
            # Random human-like actions
            if random.random() < 0.1:  # 10% chance
                self.random_scroll(driver)
            
            if random.random() < 0.05:  # 5% chance
                self.pause_resume_video(driver)
            
            time.sleep(random.uniform(5, 15))
        
        return int(watch_duration)
    
    def random_engagement(self, driver):
        engagement_actions = []
        
        try:
            # Like video (20% chance)
            if random.random() < 0.2:
                like_button = driver.find_element(By.XPATH, "//button[@aria-label='Like this video']")
                like_button.click()
                engagement_actions.append("like")
                time.sleep(random.uniform(1, 3))
        except:
            pass
        
        try:
            # Subscribe (5% chance)
            if random.random() < 0.05:
                subscribe_button = driver.find_element(By.XPATH, "//button[contains(@aria-label, 'Subscribe')]")
                subscribe_button.click()
                engagement_actions.append("subscribe")
                time.sleep(random.uniform(2, 5))
        except:
            pass
        
        try:
            # Comment (2% chance)
            if random.random() < 0.02:
                self.post_comment(driver)
                engagement_actions.append("comment")
        except:
            pass
        
        return ",".join(engagement_actions) if engagement_actions else "view_only"
    
    def post_comment(self, driver):
        try:
            # Scroll to comments section
            driver.execute_script("window.scrollTo(0, 1000);")
            time.sleep(2)
            
            # Click comment box
            comment_box = driver.find_element(By.ID, "placeholder-area")
            comment_box.click()
            time.sleep(1)
            
            # Type comment
            comment_input = driver.find_element(By.ID, "contenteditable-root")
            comment_text = random.choice(self.realistic_comments)
            comment_input.send_keys(comment_text)
            time.sleep(random.uniform(3, 8))
            
            # Submit comment
            submit_button = driver.find_element(By.ID, "submit-button")
            submit_button.click()
            time.sleep(2)
            
        except Exception as e:
            pass
    
    def random_scroll(self, driver):
        scroll_amount = random.randint(100, 500)
        driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
        time.sleep(random.uniform(1, 3))
    
    def pause_resume_video(self, driver):
        try:
            video_player = driver.find_element(By.CLASS_NAME, "video-stream")
            video_player.click()  # Pause
            time.sleep(random.uniform(2, 10))
            video_player.click()  # Resume
        except:
            pass

if __name__ == "__main__":
    app = YouTubeViralApp()
    app.run()