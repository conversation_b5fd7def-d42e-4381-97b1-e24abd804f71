#!/usr/bin/env python3
"""
Test script to verify YouTube URL validation
"""

def is_valid_youtube_url(url):
    """
    Validate YouTube URL - supports all common YouTube URL formats
    """
    if not url:
        return False
    
    # Convert to lowercase for case-insensitive matching
    url_lower = url.lower()
    
    # List of valid YouTube URL patterns
    valid_patterns = [
        # Standard YouTube URLs
        'youtube.com/watch?v=',
        'www.youtube.com/watch?v=',
        'https://youtube.com/watch?v=',
        'https://www.youtube.com/watch?v=',
        'http://youtube.com/watch?v=',
        'http://www.youtube.com/watch?v=',
        
        # YouTube short URLs
        'youtu.be/',
        'https://youtu.be/',
        'http://youtu.be/',
        'www.youtu.be/',
        
        # YouTube mobile URLs
        'm.youtube.com/watch?v=',
        'https://m.youtube.com/watch?v=',
        'http://m.youtube.com/watch?v=',
        
        # YouTube embed URLs
        'youtube.com/embed/',
        'www.youtube.com/embed/',
        'https://youtube.com/embed/',
        'https://www.youtube.com/embed/',
        
        # YouTube shorts
        'youtube.com/shorts/',
        'www.youtube.com/shorts/',
        'https://youtube.com/shorts/',
        'https://www.youtube.com/shorts/',
    ]
    
    # Check if URL matches any valid pattern
    for pattern in valid_patterns:
        if pattern in url_lower:
            return True
    
    return False

# Test cases
test_urls = [
    # Valid URLs
    "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    "https://youtube.com/watch?v=dQw4w9WgXcQ",
    "http://www.youtube.com/watch?v=dQw4w9WgXcQ",
    "www.youtube.com/watch?v=dQw4w9WgXcQ",
    "youtube.com/watch?v=dQw4w9WgXcQ",
    "https://youtu.be/dQw4w9WgXcQ",
    "youtu.be/dQw4w9WgXcQ",
    "https://m.youtube.com/watch?v=dQw4w9WgXcQ",
    "https://www.youtube.com/embed/dQw4w9WgXcQ",
    "https://www.youtube.com/shorts/dQw4w9WgXcQ",
    
    # Invalid URLs
    "https://www.google.com",
    "https://www.facebook.com/video",
    "not a url",
    "",
    "https://vimeo.com/123456",
    "https://www.dailymotion.com/video/x123456"
]

print("🔍 Testing YouTube URL Validation")
print("=" * 50)

valid_count = 0
invalid_count = 0

for url in test_urls:
    result = is_valid_youtube_url(url)
    status = "✅ VALID" if result else "❌ INVALID"
    print(f"{status}: {url}")
    
    if result:
        valid_count += 1
    else:
        invalid_count += 1

print("\n" + "=" * 50)
print(f"📊 Results: {valid_count} valid, {invalid_count} invalid URLs tested")
print("🎉 URL validation is working properly!")
